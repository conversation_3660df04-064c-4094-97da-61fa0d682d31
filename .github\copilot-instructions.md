# GitHub Copilot Instructions for WPX Controller

## Project Overview

WPX Controller is a PyQt5-based desktop application for wireless power testing device control. It features real-time monitoring, protocol communication, and automated firmware updates via serial communication.

## Architecture Components

### Device Communication Layer
- **`controller/pd_pocket.py`**: Core device interface with `PDPocket` class for device discovery and control
- **`controller/wpx_protocol.py`**: WPX protocol implementation with frame structure (SOF 0xEE + Length + Seq + CRC8 + CMD + Data + CRC16)
- **`controller/serial_reader.py`**: Threaded serial communication handler using Qt signals
- **Device VID/PID**: Normal mode `0x0906:0x5A3E`, Bootloader mode `0x07E7:0x5A42`

### GUI Framework Structure
- **`gui_source/mdp_gui.py`**: Main application (7800+ lines) containing all dialog classes and firmware update logic
- **`gui_source/mdp_main.py`**: Application entry point with PyInstaller splash screen integration
- **`gui_source/mdp_gui_template/`**: Qt Designer generated UI components (.ui → .py conversion)
- **`gui_source/qframelesswindow/`**: Custom cross-platform frameless window implementation

### Firmware Update System
- **Complex multi-phase system**: System firmware → Device reconnection → Kernel update → Full update completion
- **YModem protocol**: Used for actual firmware flashing operations
- **Auto-detection**: Supports both normal and bootloader mode devices
- **Thread-safe UI updates**: Uses Qt signals/slots for progress reporting

## Development Workflow

### Environment Setup
```bash
cd gui_source
python -m pip install -U uv
uv venv --python 3.11
.venv\Scripts\activate  # Windows
uv pip install -r requirements.txt
```

### Building Applications
```powershell
# Parallel builds (recommended)
.\build-x64.ps1

# Individual builds
pyinstaller mdp.spec              # Standard build
pyinstaller mdp_numba.spec       # Numba-optimized build
pyinstaller mdp_linux.spec       # Linux build
```

### UI Development
```bash
# Convert Qt Designer files to Python
pyuic5 mainwindow.ui -o mainwindow_ui.py
```

## Code Patterns and Conventions

### Device Connection Pattern
```python
from controller import find_all_devices, PDPocket

# Device discovery
devices = find_all_devices()
bootloader_devices = find_bootloader_devices()

# Connection
api = PDPocket(port=port, baudrate=115200, use_wpx_protocol=True)
MainWindow.api = api  # Global API instance pattern
```

### Threading and UI Updates
```python
# CRITICAL: Always use Qt signals for UI updates from threads
class UIUpdateSignals(QtCore.QObject):
    progress_update_signal = QtCore.pyqtSignal(int)
    status_update_signal = QtCore.pyqtSignal(str)

# Thread-safe UI updates
self.ui_signals.progress_update_signal.emit(progress)
QtCore.QMetaObject.invokeMethod(self, "method_name", QtCore.Qt.QueuedConnection)
```

### Firmware Update Architecture
```python
# Multi-phase update structure
def _start_full_update_phase_system(self):
    # Phase 1: System firmware
def _start_full_update_phase_reconnect(self):
    # Phase 2: Wait for device restart
def _start_full_update_phase_kernel(self):
    # Phase 3: Kernel update

# Global state management
self.full_update_state = {
    'is_running': True,
    'current_phase': 'system'  # 'system' → 'reconnect' → 'kernel'
}
```

### Error Handling Pattern
```python
# Use loguru for consistent logging
from loguru import logger

try:
    # Operation
    logger.info("Starting operation")
except Exception as e:
    logger.error(f"Operation failed: {e}")
    # Always restore UI state on error
    self.ui.button.setText("Original Text")
    self.ui.button.setEnabled(True)
```

## Critical Implementation Details

### Serial Port Management
- **Global API instance**: `MainWindow.api` shared across components
- **Thread conflicts**: Must stop data reading before firmware updates using `_ensure_data_reading_stopped()`
- **Connection cleanup**: Always call `_cleanup_all_connections()` before major operations

### Dialog Class Structure
All dialogs inherit from `FramelessWindow` and follow this pattern:
```python
class MyDialog(QtWidgets.QDialog, FramelessWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.ui = Ui_DialogName()
        self.ui.setupUi(self)
        self.CustomTitleBar = CustomTitleBar(self, "Title")
        self.setTitleBar(self.CustomTitleBar)
```

### Build Configuration
- **PyInstaller specs**: `mdp.spec` (standard), `mdp_numba.spec` (optimized), `mdp_linux.spec`
- **Asset inclusion**: Icons, fonts (`SarasaFixedSC-SemiBold.ttf`), translation files, battery curves
- **UPX compression**: Integrated for size optimization
- **Parallel builds**: Use PowerShell script for efficiency

## Dependencies and Tools

### Core Dependencies
- PyQt5 (GUI framework)
- pyqtgraph (real-time plotting)
- serial/pyserial (hardware communication)
- loguru (logging)
- numba (performance optimization - optional)

### Development Tools
- uv (fast Python package manager)
- PyInstaller (application packaging)
- UPX (executable compression)
- Qt Designer (UI design)

## Testing and Debugging

### Manual Testing
```python
# Test device connection
python -c "from controller import find_all_devices; print(find_all_devices())"

# Enable debug logging
python mdp_main.py --debug
# OR
set MDP_ENABLE_LOG=1 && python mdp_main.py
```

### Key Debugging Areas
- **Serial port conflicts**: Check for multiple threads accessing same port
- **Firmware update failures**: Monitor device mode transitions (normal ↔ bootloader)
- **UI freezing**: Ensure long operations run in background threads
- **Memory issues**: Use Numba build for performance-critical operations

## Important Files to Understand
- `mdp_gui.py` lines 4171-6000: Firmware update implementation
- `controller/wpx_protocol.py`: Protocol frame structure and commands
- `build-x64.ps1`: Production build process
- `FIRMWARE_UPGRADE_REFACTOR_SUMMARY.md`: Recent firmware system changes

from PyQt5 import QtWidgets, QtCore, QtGui


class AppleStyleDisplay(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._value = 0.0
        self._precision = 2
        self._unit = ""
        self.setMinimumSize(200, 50)
        self.setMaximumSize(280, 60)
        
        # 根据组件名称自动设置单位和颜色（稍后在setObjectName时设置）
        self._auto_unit = True
        self._color_theme = "default"  # default, voltage, current, power
        
    def setValue(self, value):
        try:
            if value == '' or value is None:
                self._value = 0.0
            elif isinstance(value, str):
                # 处理格式化的字符串，移除可能的单位
                clean_value = value.strip()
                if clean_value == '':
                    self._value = 0.0
                else:
                    # 尝试提取数字部分
                    import re
                    number_match = re.search(r'[-+]?(\d+\.?\d*|\.\d+)([eE][-+]?\d+)?', clean_value)
                    if number_match:
                        self._value = float(number_match.group(0))
                    else:
                        self._value = 0.0
            else:
                self._value = float(value)
        except (ValueError, TypeError):
            self._value = 0.0
        self.update()
        
    def value(self):
        return self._value
        
    def setPrecision(self, precision):
        self._precision = precision
        self.update()
        
    def setUnit(self, unit):
        self._unit = unit
        self._auto_unit = False  # 手动设置单位后禁用自动检测
        self.update()
        
    def setObjectName(self, name):
        super().setObjectName(name)
        # 根据组件名称自动设置单位和颜色主题
        if self._auto_unit:
            if 'Voltage' in name:
                self._unit = "V"
                self._color_theme = "voltage"
            elif 'Current' in name:
                self._unit = "A"
                self._color_theme = "current"
            elif 'Power' in name or 'AvgPower' in name:
                self._unit = "W"
                self._color_theme = "power"
            elif 'Energe' in name:
                self._unit = "J"
                self._color_theme = "default"
            elif 'Resistence' in name:
                self._unit = "Wh"
                self._color_theme = "default"
        
    def display(self, value):
        # 兼容QLCDNumber的display方法
        # 主程序使用这个方法传递格式化的字符串
        self.setValue(value)
        
    def setDigitCount(self, count):
        # 兼容QLCDNumber的setDigitCount方法
        # 可以根据数字数量调整字体大小或精度
        if count <= 4:
            self.setPrecision(1)
        elif count <= 6:
            self.setPrecision(2)
        else:
            self.setPrecision(3)
            
    def setSmallDecimalPoint(self, small):
        # 兼容QLCDNumber的setSmallDecimalPoint方法
        pass
        
    def setSegmentStyle(self, style):
        # 兼容QLCDNumber的setSegmentStyle方法
        pass
        
    def setMode(self, mode):
        # 兼容QLCDNumber的setMode方法
        pass
        
    def setProperty(self, name, value):
        # 兼容QLCDNumber的setProperty方法
        if name == "value":
            self.setValue(value)
        elif name == "intValue":
            try:
                self.setValue(int(value) if value != '' and value is not None else 0)
            except (ValueError, TypeError):
                self.setValue(0)
        else:
            super().setProperty(name, value)
            
    def _getColorTheme(self):
        """获取当前主题的颜色配置 - 基于苹果设计指南的现代化配色"""
        if self._color_theme == "voltage":
            # 苹果绿色系 - 扁平化设计
            return {
                'bg_top': QtGui.QColor(52, 199, 89, 200),   # 苹果绿，扁平化实色
                'bg_bottom': QtGui.QColor(48, 176, 199, 200), # 备用色
                'border': QtGui.QColor(46, 179, 79, 255),   # 绿色边框，实色
                'text': QtGui.QColor(255, 255, 255, 255),   # 纯白色文字
                'unit': QtGui.QColor(255, 255, 255, 200),   # 稍透明白色单位
                'accent': QtGui.QColor(52, 199, 89, 255)    # 实色
            }
        elif self._color_theme == "current":
            # 苹果蓝色系 - 扁平化设计
            return {
                'bg_top': QtGui.QColor(0, 122, 255, 200),   # 苹果蓝，扁平化实色
                'bg_bottom': QtGui.QColor(10, 132, 255, 200), # 备用色
                'border': QtGui.QColor(0, 108, 230, 255),   # 蓝色边框，实色
                'text': QtGui.QColor(255, 255, 255, 255),   # 纯白色文字
                'unit': QtGui.QColor(255, 255, 255, 200),   # 稍透明白色单位
                'accent': QtGui.QColor(0, 122, 255, 255)    # 实色
            }
        elif self._color_theme == "power":
            # 苹果红色系 - 扁平化设计
            return {
                'bg_top': QtGui.QColor(255, 59, 48, 200),   # 苹果红，扁平化实色
                'bg_bottom': QtGui.QColor(255, 69, 58, 200), # 备用色
                'border': QtGui.QColor(230, 45, 38, 255),   # 红色边框，实色
                'text': QtGui.QColor(255, 255, 255, 255),   # 纯白色文字
                'unit': QtGui.QColor(255, 255, 255, 200),   # 稍透明白色单位
                'accent': QtGui.QColor(255, 59, 48, 255)    # 实色
            }
        else:  # default - 苹果灰色系
            return {
                'bg_top': QtGui.QColor(72, 72, 74, 255),    # 苹果中性灰，实色
                'bg_bottom': QtGui.QColor(90, 90, 92, 255), # 备用色
                'border': QtGui.QColor(60, 60, 62, 255),    # 灰色边框，实色
                'text': QtGui.QColor(255, 255, 255, 255),   # 纯白色文字
                'unit': QtGui.QColor(255, 255, 255, 200),   # 稍透明白色单位
                'accent': QtGui.QColor(72, 72, 74, 255)     # 实色
            }
        
    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        painter.setRenderHint(QtGui.QPainter.TextAntialiasing)
        
        rect = self.rect()
        colors = self._getColorTheme()
        
        # 扁平化背景 - 纯色，无渐变
        painter.setBrush(QtGui.QBrush(colors['bg_top']))
        painter.setPen(QtCore.Qt.NoPen)
        painter.drawRoundedRect(rect, 12, 12)
        
        # 简洁边框 - 可选
        painter.setBrush(QtCore.Qt.NoBrush)
        painter.setPen(QtGui.QPen(colors['border'], 1))
        border_rect = rect.adjusted(1, 1, -1, -1)
        painter.drawRoundedRect(border_rect, 11, 11)
        
        # 文本显示
        # 主数值 - 放大字体
        painter.setPen(colors['text'])
        
        font = QtGui.QFont("PingFang", 22, QtGui.QFont.Medium)  # 从17增加到22
        if not font.exactMatch():
            font = QtGui.QFont("PingFang", 22, QtGui.QFont.Medium)
        painter.setFont(font)
        
        # 格式化数值
        if self._precision == 0:
            value_text = f"{int(self._value)}"
        else:
            value_text = f"{self._value:.{self._precision}f}"
        
        # 计算文本布局
        if self._unit:
            # 分别绘制数值和单位
            text_rect = rect.adjusted(12, 0, -12, 0)
            
            # 数值部分
            value_metrics = painter.fontMetrics()
            value_width = value_metrics.horizontalAdvance(value_text)
            unit_width = value_metrics.horizontalAdvance(f" {self._unit}")
            total_width = value_width + unit_width
            
            start_x = text_rect.center().x() - total_width // 2
            
            # 绘制数值
            value_rect = QtCore.QRect(start_x, text_rect.y(), value_width, text_rect.height())
            painter.drawText(value_rect, QtCore.Qt.AlignCenter, value_text)
            
            # 绘制单位（稍微透明）
            painter.setPen(colors['unit'])
            unit_font = QtGui.QFont("PingFang")
            unit_font.setPointSize(max(16, font.pointSize() - 4))  # 单位字体也相应放大
            painter.setFont(unit_font)
            
            unit_rect = QtCore.QRect(start_x + value_width, text_rect.y(), unit_width, text_rect.height())
            painter.drawText(unit_rect, QtCore.Qt.AlignCenter, f" {self._unit}")
        else:
            # 只有数值
            text_rect = rect.adjusted(12, 0, -12, 0)
            painter.drawText(text_rect, QtCore.Qt.AlignCenter, value_text)
        
    def sizeHint(self):
        return QtCore.QSize(200, 50)
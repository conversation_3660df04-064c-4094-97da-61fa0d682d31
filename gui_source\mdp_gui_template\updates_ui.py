# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'mdp_gui_template\updates.ui'
#
# Created by: PyQt5 UI code generator 5.15.6
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_DialogSettings(object):
    def setupUi(self, DialogSettings):
        DialogSettings.setObjectName("DialogSettings")
        DialogSettings.setWindowModality(QtCore.Qt.ApplicationModal)
        DialogSettings.setEnabled(True)
        DialogSettings.resize(303, 460)
        DialogSettings.setMinimumSize(QtCore.QSize(303, 460))
        DialogSettings.setMaximumSize(QtCore.QSize(303, 460))
        DialogSettings.setSizeGripEnabled(False)
        DialogSettings.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(DialogSettings)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label_2 = QtWidgets.QLabel(DialogSettings)
        self.label_2.setAlignment(QtCore.Qt.AlignCenter)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout.addWidget(self.label_2)
        self.label_3 = QtWidgets.QLabel(DialogSettings)
        self.label_3.setAlignment(QtCore.Qt.AlignCenter)
        self.label_3.setObjectName("label_3")
        self.horizontalLayout.addWidget(self.label_3)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.horizontalLayout_33 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_33.setObjectName("horizontalLayout_33")
        self.pushButton = QtWidgets.QPushButton(DialogSettings)
        self.pushButton.setObjectName("pushButton")
        self.horizontalLayout_33.addWidget(self.pushButton)
        self.verticalLayout.addLayout(self.horizontalLayout_33)
        self.line = QtWidgets.QFrame(DialogSettings)
        self.line.setFrameShadow(QtWidgets.QFrame.Plain)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setObjectName("line")
        self.verticalLayout.addWidget(self.line)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout()
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.label_44 = QtWidgets.QLabel(DialogSettings)
        self.label_44.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.label_44.setFont(font)
        self.label_44.setAlignment(QtCore.Qt.AlignCenter)
        self.label_44.setObjectName("label_44")
        self.verticalLayout_2.addWidget(self.label_44)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.label = QtWidgets.QLabel(DialogSettings)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.horizontalLayout_2.addWidget(self.label)
        self.fw_version_lb = QtWidgets.QLabel(DialogSettings)
        self.fw_version_lb.setAlignment(QtCore.Qt.AlignCenter)
        self.fw_version_lb.setObjectName("fw_version_lb")
        self.horizontalLayout_2.addWidget(self.fw_version_lb)
        self.verticalLayout_2.addLayout(self.horizontalLayout_2)
        self.verticalLayout.addLayout(self.verticalLayout_2)
        self.btnFWupdatekKey = QtWidgets.QPushButton(DialogSettings)
        self.btnFWupdatekKey.setObjectName("btnFWupdatekKey")
        self.verticalLayout.addWidget(self.btnFWupdatekKey)
        self.line_2 = QtWidgets.QFrame(DialogSettings)
        self.line_2.setFrameShadow(QtWidgets.QFrame.Plain)
        self.line_2.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_2.setObjectName("line_2")
        self.verticalLayout.addWidget(self.line_2)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setContentsMargins(-1, -1, -1, 0)
        self.horizontalLayout_3.setSpacing(6)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.btnOk = QtWidgets.QPushButton(DialogSettings)
        font = QtGui.QFont()
        font.setFamily("PingFang")
        self.btnOk.setFont(font)
        self.btnOk.setObjectName("btnOk")
        self.horizontalLayout_3.addWidget(self.btnOk)
        self.horizontalLayout_3.setStretch(0, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_3)

        self.retranslateUi(DialogSettings)
        QtCore.QMetaObject.connectSlotsByName(DialogSettings)

    def retranslateUi(self, DialogSettings):
        _translate = QtCore.QCoreApplication.translate
        DialogSettings.setWindowTitle(_translate("DialogSettings", "系统设置"))
        self.label_2.setText(_translate("DialogSettings", "上位机版本："))
        self.label_3.setText(_translate("DialogSettings", "v1.0.0"))
        self.pushButton.setText(_translate("DialogSettings", "检查更新"))
        self.label_44.setText(_translate("DialogSettings", "- WPX固件更新 -"))
        self.label.setText(_translate("DialogSettings", "固件版本："))
        self.fw_version_lb.setText(_translate("DialogSettings", "0.0.0"))
        self.btnFWupdatekKey.setText(_translate("DialogSettings", "固件更新"))
        self.btnOk.setText(_translate("DialogSettings", "OK"))

<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1374</width>
    <height>975</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>WPX-01 无线充电测试仪上位机</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>../icon.ico</normaloff>../icon.ico</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_8" stretch="0,0,1">
    <item>
     <spacer name="verticalSpacer_top">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeType">
       <enum>QSizePolicy::Fixed</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>30</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <layout class="QVBoxLayout" name="verticalLayout_23"/>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_13" stretch="0,1">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_12">
        <property name="spacing">
         <number>6</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_bottom" stretch="1,0">
        <property name="spacing">
         <number>10</number>
        </property>
        <item>
         <widget class="QFrame" name="frameGraph">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="font">
           <font>
            <family>PingFang</family>
           </font>
          </property>
          <property name="frameShape">
           <enum>QFrame::StyledPanel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Plain</enum>
          </property>
          <property name="lineWidth">
           <number>2</number>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_18">
           <property name="leftMargin">
            <number>3</number>
           </property>
           <property name="topMargin">
            <number>3</number>
           </property>
           <property name="rightMargin">
            <number>3</number>
           </property>
           <property name="bottomMargin">
            <number>3</number>
           </property>
           <item>
            <widget class="QFrame" name="frameLcd">
             <property name="font">
              <font>
               <family>PingFang</family>
              </font>
             </property>
             <property name="frameShape">
              <enum>QFrame::StyledPanel</enum>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Plain</enum>
             </property>
             <property name="lineWidth">
              <number>2</number>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_19">
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>6</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>6</number>
              </property>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="1,1,1">
                <property name="spacing">
                 <number>0</number>
                </property>
                <item>
                 <layout class="QVBoxLayout" name="verticalLayout">
                  <property name="spacing">
                   <number>10</number>
                  </property>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,5">
                    <item>
                     <widget class="QLabel" name="label">
                      <property name="minimumSize">
                       <size>
                        <width>0</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>PingFang</family>
                        <pointsize>13</pointsize>
                        <weight>75</weight>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>  电压</string>
                      </property>
                      <property name="scaledContents">
                       <bool>false</bool>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignCenter</set>
                      </property>
                      <property name="margin">
                       <number>6</number>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="AppleStyleDisplay" name="lcdVoltage" native="true">
                      <property name="enabled">
                       <bool>true</bool>
                      </property>
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Ignored" vsizetype="Minimum">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>200</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>280</width>
                        <height>60</height>
                       </size>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="2,5">
                    <item>
                     <widget class="QLabel" name="label_2">
                      <property name="minimumSize">
                       <size>
                        <width>0</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>PingFang</family>
                        <pointsize>13</pointsize>
                        <weight>75</weight>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>  电流</string>
                      </property>
                      <property name="scaledContents">
                       <bool>false</bool>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignCenter</set>
                      </property>
                      <property name="margin">
                       <number>6</number>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="AppleStyleDisplay" name="lcdCurrent" native="true">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Ignored" vsizetype="Minimum">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>200</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>280</width>
                        <height>60</height>
                       </size>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </item>
                <item>
                 <layout class="QVBoxLayout" name="verticalLayout_2">
                  <property name="spacing">
                   <number>10</number>
                  </property>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="2,5">
                    <item>
                     <widget class="QLabel" name="label_3">
                      <property name="font">
                       <font>
                        <family>PingFang</family>
                        <pointsize>13</pointsize>
                        <weight>75</weight>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>  功率</string>
                      </property>
                      <property name="scaledContents">
                       <bool>false</bool>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignCenter</set>
                      </property>
                      <property name="margin">
                       <number>6</number>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="AppleStyleDisplay" name="lcdPower" native="true">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Ignored" vsizetype="Minimum">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>200</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>280</width>
                        <height>60</height>
                       </size>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="2,5">
                    <item>
                     <widget class="QLabel" name="label_4">
                      <property name="font">
                       <font>
                        <family>PingFang</family>
                        <pointsize>13</pointsize>
                        <weight>75</weight>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>  容量</string>
                      </property>
                      <property name="scaledContents">
                       <bool>false</bool>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignCenter</set>
                      </property>
                      <property name="margin">
                       <number>6</number>
                      </property>
                      <property name="indent">
                       <number>-1</number>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="AppleStyleDisplay" name="lcdEnerge" native="true">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Ignored" vsizetype="Minimum">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>200</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>280</width>
                        <height>60</height>
                       </size>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </item>
                <item>
                 <layout class="QVBoxLayout" name="verticalLayout_3">
                  <property name="spacing">
                   <number>10</number>
                  </property>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="2,5">
                    <item>
                     <widget class="QLabel" name="label_5">
                      <property name="font">
                       <font>
                        <family>PingFang</family>
                        <pointsize>13</pointsize>
                        <weight>75</weight>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>平均功率</string>
                      </property>
                      <property name="scaledContents">
                       <bool>false</bool>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignCenter</set>
                      </property>
                      <property name="margin">
                       <number>6</number>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="AppleStyleDisplay" name="lcdAvgPower" native="true">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Ignored" vsizetype="Minimum">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>200</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>280</width>
                        <height>60</height>
                       </size>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="2,5">
                    <item>
                     <widget class="QLabel" name="label_6">
                      <property name="font">
                       <font>
                        <family>PingFang</family>
                        <pointsize>13</pointsize>
                        <weight>75</weight>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>能量</string>
                      </property>
                      <property name="scaledContents">
                       <bool>false</bool>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignCenter</set>
                      </property>
                      <property name="margin">
                       <number>6</number>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="AppleStyleDisplay" name="lcdResistence" native="true">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Ignored" vsizetype="Minimum">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>200</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>280</width>
                        <height>60</height>
                       </size>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_7" stretch="0,0,0">
             <property name="spacing">
              <number>6</number>
             </property>
             <property name="leftMargin">
              <number>3</number>
             </property>
             <property name="topMargin">
              <number>8</number>
             </property>
             <property name="rightMargin">
              <number>3</number>
             </property>
             <property name="bottomMargin">
              <number>6</number>
             </property>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_27">
               <property name="topMargin">
                <number>12</number>
               </property>
               <property name="bottomMargin">
                <number>12</number>
               </property>
               <item>
                <widget class="QLabel" name="label_13">
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="text">
                  <string>X/Y数据:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QComboBox" name="comboGraph1Data">
                 <property name="minimumSize">
                  <size>
                   <width>90</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>90</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="toolTip">
                  <string>上窗口数据</string>
                 </property>
                 <item>
                  <property name="text">
                   <string>电压</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>电流</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>功率</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>核心温度</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>负载温度</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>外部探头温度</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>整流桥温度</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>线圈温度</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>无</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item>
                <widget class="QComboBox" name="comboGraph2Data">
                 <property name="minimumSize">
                  <size>
                   <width>90</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>90</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="toolTip">
                  <string>下窗口数据</string>
                 </property>
                 <property name="currentIndex">
                  <number>1</number>
                 </property>
                 <item>
                  <property name="text">
                   <string>电压</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>电流</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>功率</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>核心温度</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>负载温度</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>外部探头温度</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>整流桥温度</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>线圈温度</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>无</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_29">
                 <property name="minimumSize">
                  <size>
                   <width>25</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="text">
                  <string>采样</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelFps">
                 <property name="minimumSize">
                  <size>
                   <width>50</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="toolTip">
                  <string>实时采样率</string>
                 </property>
                 <property name="text">
                  <string>0.0Hz</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer">
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QPushButton" name="btnGraphRecord">
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>80</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="toolTip">
                  <string>将原始数据记录到CSV文件，不受图形缓冲区限制</string>
                 </property>
                 <property name="text">
                  <string>录制</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btnGraphDump">
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>80</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="toolTip">
                  <string>导出当前数据缓冲区的有效数据</string>
                 </property>
                 <property name="text">
                  <string>导出</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btnGraphAutoScale">
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>80</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="toolTip">
                  <string>切换数据波形是否自动适应窗口</string>
                 </property>
                 <property name="text">
                  <string>适应</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btnRecordFloatWindow">
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>80</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="toolTip">
                  <string>切换数据监控悬浮窗</string>
                 </property>
                 <property name="text">
                  <string>浮窗</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btnRecordClear">
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>80</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="toolTip">
                  <string>清零平均功率和能量累计</string>
                 </property>
                 <property name="text">
                  <string>回零</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btnGraphClear">
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>80</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="toolTip">
                  <string>清空波形数据缓冲区</string>
                 </property>
                 <property name="text">
                  <string>清空</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btnGraphKeep">
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>80</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>PingFang</family>
                  </font>
                 </property>
                 <property name="toolTip">
                  <string>停止数据波形刷新（数据缓冲区仍在更新）</string>
                 </property>
                 <property name="text">
                  <string>暂停</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer_4">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QFrame" name="frameGraphContainer">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>1</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>100</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayoutGraphContainer">
                <property name="spacing">
                 <number>2</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="PlotWidget" name="widgetGraph1" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>480</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>PingFang</family>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QFrame" name="frameThumbnailContainer">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>70</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>70</height>
                   </size>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QHBoxLayout" name="horizontalLayoutThumbnail">
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="QWidget" name="widgetThumbnailPlaceholder" native="true">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>100</height>
                      </size>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_settings">
          <property name="spacing">
           <number>10</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_12">
            <property name="spacing">
             <number>10</number>
            </property>
           </layout>
          </item>
          <item>
           <widget class="QFrame" name="frameOutputSetting">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="maximumSize">
             <size>
              <width>380</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>PingFang</family>
             </font>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <property name="lineWidth">
             <number>1</number>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_11">
             <property name="leftMargin">
              <number>3</number>
             </property>
             <property name="topMargin">
              <number>3</number>
             </property>
             <property name="rightMargin">
              <number>3</number>
             </property>
             <property name="bottomMargin">
              <number>3</number>
             </property>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_6" stretch="0,0,0">
               <item>
                <layout class="QGridLayout" name="gridLayout_data">
                 <property name="leftMargin">
                  <number>9</number>
                 </property>
                 <property name="topMargin">
                  <number>9</number>
                 </property>
                 <property name="rightMargin">
                  <number>9</number>
                 </property>
                 <property name="bottomMargin">
                  <number>9</number>
                 </property>
                 <item row="2" column="1">
                  <widget class="QLabel" name="run_time_lb">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>00:00:00</string>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="4">
                  <widget class="QLabel" name="potential_power_lb">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0W</string>
                   </property>
                  </widget>
                 </item>
                 <item row="3" column="2">
                  <widget class="Line" name="line_6">
                   <property name="orientation">
                    <enum>Qt::Vertical</enum>
                   </property>
                  </widget>
                 </item>
                 <item row="3" column="0">
                  <widget class="QLabel" name="Coil_temp_bt">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>线圈温度:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="5" column="2">
                  <widget class="Line" name="line_8">
                   <property name="orientation">
                    <enum>Qt::Vertical</enum>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="4">
                  <widget class="QLabel" name="signal_strength_lb">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>0</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0%</string>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="0">
                  <widget class="QLabel" name="label_16">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>握手功率:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="4" column="2">
                  <widget class="Line" name="line_7">
                   <property name="orientation">
                    <enum>Qt::Vertical</enum>
                   </property>
                  </widget>
                 </item>
                 <item row="4" column="0">
                  <widget class="QLabel" name="label_24">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>核心温度:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="3" column="3">
                  <widget class="QLabel" name="label_39">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>RP:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="3">
                  <widget class="QLabel" name="label_12">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>信号强度:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="5" column="0">
                  <widget class="QLabel" name="label_30">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>负载温度:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="6" column="4">
                  <widget class="QLabel" name="ext_probe_temp_lb">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0℃</string>
                   </property>
                  </widget>
                 </item>
                 <item row="6" column="3">
                  <widget class="QLabel" name="label_26">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>外部探头温度:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="7" column="2">
                  <widget class="Line" name="line_10">
                   <property name="orientation">
                    <enum>Qt::Vertical</enum>
                   </property>
                  </widget>
                 </item>
                 <item row="2" column="4">
                  <widget class="QLabel" name="ce_lb">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0</string>
                   </property>
                  </widget>
                 </item>
                 <item row="3" column="4">
                  <widget class="QLabel" name="rpp_lb">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0</string>
                   </property>
                  </widget>
                 </item>
                 <item row="3" column="1">
                  <widget class="QLabel" name="coil_temp_lb">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0℃</string>
                   </property>
                  </widget>
                 </item>
                 <item row="5" column="1">
                  <widget class="QLabel" name="eload_temp_lb">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0℃</string>
                   </property>
                  </widget>
                 </item>
                 <item row="5" column="3">
                  <widget class="QLabel" name="label_31">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>整流桥温度:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="2" column="3">
                  <widget class="QLabel" name="label_8">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>CE:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="4" column="4">
                  <widget class="QLabel" name="freq_lb">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>0</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0 Khz</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <widget class="QLabel" name="test_protocol_lb">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>0</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>-</string>
                   </property>
                  </widget>
                 </item>
                 <item row="4" column="1">
                  <widget class="QLabel" name="core_temp_lb">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0℃</string>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="1">
                  <widget class="QLabel" name="nego_power_lb">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0W</string>
                   </property>
                  </widget>
                 </item>
                 <item row="4" column="3">
                  <widget class="QLabel" name="label_7">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>工作频率:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="3">
                  <widget class="QLabel" name="label_17">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>潜在功率:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="2">
                  <widget class="Line" name="line_4">
                   <property name="orientation">
                    <enum>Qt::Vertical</enum>
                   </property>
                  </widget>
                 </item>
                 <item row="6" column="2">
                  <widget class="Line" name="line_9">
                   <property name="orientation">
                    <enum>Qt::Vertical</enum>
                   </property>
                  </widget>
                 </item>
                 <item row="5" column="4">
                  <widget class="QLabel" name="bridge_temp_lb">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>0℃</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="0">
                  <widget class="QLabel" name="label_11">
                   <property name="font">
                    <font>
                     <family>AniMe Matrix - MB_EN</family>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>握手协议:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="2" column="0">
                  <widget class="QLabel" name="label_19">
                   <property name="font">
                    <font>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>设备时间:</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="2">
                  <widget class="Line" name="line_5">
                   <property name="orientation">
                    <enum>Qt::Vertical</enum>
                   </property>
                  </widget>
                 </item>
                 <item row="2" column="2">
                  <widget class="Line" name="line_11">
                   <property name="orientation">
                    <enum>Qt::Vertical</enum>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="Line" name="line_3">
                 <property name="frameShadow">
                  <enum>QFrame::Plain</enum>
                 </property>
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QFrame" name="frame">
                 <property name="frameShape">
                  <enum>QFrame::Box</enum>
                 </property>
                 <property name="frameShadow">
                  <enum>QFrame::Plain</enum>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_27">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <widget class="QTabWidget" name="tabWidget">
                    <property name="enabled">
                     <bool>true</bool>
                    </property>
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>0</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="font">
                     <font>
                      <family>PingFang</family>
                     </font>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">QTabWidget::pane {
    border-radius: 6px;
    margin-top: -1px;
}

QTabWidget::tab-bar {
    alignment: center;
}

QTabBar::tab {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    min-width: 30px;
    min-height: 20px;
    padding: 4px 8px;
    margin-right: 2px;
}</string>
                    </property>
                    <property name="tabPosition">
                     <enum>QTabWidget::North</enum>
                    </property>
                    <property name="tabShape">
                     <enum>QTabWidget::Rounded</enum>
                    </property>
                    <property name="currentIndex">
                     <number>1</number>
                    </property>
                    <property name="iconSize">
                     <size>
                      <width>5</width>
                      <height>5</height>
                     </size>
                    </property>
                    <property name="elideMode">
                     <enum>Qt::ElideMiddle</enum>
                    </property>
                    <property name="usesScrollButtons">
                     <bool>false</bool>
                    </property>
                    <property name="documentMode">
                     <bool>true</bool>
                    </property>
                    <property name="tabsClosable">
                     <bool>false</bool>
                    </property>
                    <property name="movable">
                     <bool>false</bool>
                    </property>
                    <property name="tabBarAutoHide">
                     <bool>false</bool>
                    </property>
                    <widget class="QWidget" name="tabPower">
                     <attribute name="title">
                      <string>调试</string>
                     </attribute>
                     <attribute name="toolTip">
                      <string>调试设置</string>
                     </attribute>
                     <layout class="QVBoxLayout" name="verticalLayout_10">
                      <item>
                       <widget class="QGroupBox" name="groupBoxChargeConfig_4">
                        <property name="font">
                         <font>
                          <family>PingFang</family>
                          <weight>75</weight>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="title">
                         <string>🔋 协议配置</string>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_22">
                         <item>
                          <widget class="QWidget" name="horizontalWidget" native="true">
                           <layout class="QHBoxLayout" name="horizontalLayout_16">
                            <item>
                             <widget class="QLabel" name="set_protocol_lb">
                              <property name="text">
                               <string>运行协议：</string>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QComboBox" name="set_protocol_cb">
                              <item>
                               <property name="text">
                                <string>BPP 5W</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>EPP 15W</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Qi2 MPP 15W</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Qi2.2 MPP 25W</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Apple 7.5W</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Apple MagSafe 15W</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>FAKE MagSafe</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>PPDE 10W</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Sumsung 15W</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Xiaomi </string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Huawei</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>OPPO</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>VIVO</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Redmi</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Realme</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>iQOO</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Meizu</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Honor</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>Generic</string>
                               </property>
                              </item>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="set_protocol_bt">
                              <property name="cursor">
                               <cursorShape>PointingHandCursor</cursorShape>
                              </property>
                              <property name="text">
                               <string>设置</string>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                         <item>
                          <widget class="Line" name="line">
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QWidget" name="horizontalWidget" native="true">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
                             <horstretch>0</horstretch>
                             <verstretch>20</verstretch>
                            </sizepolicy>
                           </property>
                           <layout class="QHBoxLayout" name="horizontalLayout_17">
                            <item>
                             <widget class="QLabel" name="label_10">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="text">
                               <string>FOD补偿：</string>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QLabel" name="fod_offset_lb">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="text">
                               <string>+500</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="set_fod_offset_bt">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="cursor">
                               <cursorShape>PointingHandCursor</cursorShape>
                              </property>
                              <property name="focusPolicy">
                               <enum>Qt::StrongFocus</enum>
                              </property>
                              <property name="text">
                               <string>设置</string>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                         <item>
                          <widget class="QSlider" name="set_fod_offset_Slider">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="cursor">
                            <cursorShape>OpenHandCursor</cursorShape>
                           </property>
                           <property name="acceptDrops">
                            <bool>false</bool>
                           </property>
                           <property name="autoFillBackground">
                            <bool>false</bool>
                           </property>
                           <property name="minimum">
                            <number>-2000</number>
                           </property>
                           <property name="maximum">
                            <number>2000</number>
                           </property>
                           <property name="value">
                            <number>500</number>
                           </property>
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                           <property name="invertedAppearance">
                            <bool>false</bool>
                           </property>
                           <property name="invertedControls">
                            <bool>false</bool>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="Line" name="line_13">
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_24">
                           <item>
                            <widget class="QLabel" name="label_64">
                             <property name="font">
                              <font>
                               <pointsize>10</pointsize>
                              </font>
                             </property>
                             <property name="text">
                              <string>负载电压电流设置:(非必要请勿修改)</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_19">
                           <item>
                            <widget class="QLineEdit" name="set_volta_ld">
                             <property name="text">
                              <string>6.0</string>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLabel" name="label_66">
                             <property name="text">
                              <string>V</string>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLineEdit" name="set_current_ld">
                             <property name="text">
                              <string>1.0</string>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLabel" name="label_67">
                             <property name="text">
                              <string>A</string>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QPushButton" name="set_power_data_bt">
                             <property name="sizePolicy">
                              <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                               <horstretch>10</horstretch>
                               <verstretch>0</verstretch>
                              </sizepolicy>
                             </property>
                             <property name="text">
                              <string>设置</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="send_bat_full_bt">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>0</width>
                          <height>25</height>
                         </size>
                        </property>
                        <property name="text">
                         <string>发送电池充满指令</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QGroupBox" name="groupBoxChargeControl_3">
                        <property name="font">
                         <font>
                          <family>PingFang</family>
                          <weight>75</weight>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="title">
                         <string>⚡ 协议控制</string>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_24">
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_22">
                           <item>
                            <widget class="QLabel" name="send_ce_lb">
                             <property name="minimumSize">
                              <size>
                               <width>0</width>
                               <height>25</height>
                              </size>
                             </property>
                             <property name="font">
                              <font>
                               <family>PingFang</family>
                               <weight>75</weight>
                               <bold>true</bold>
                              </font>
                             </property>
                             <property name="text">
                              <string>Control Error:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QSpinBox" name="send_ce_sp">
                             <property name="sizePolicy">
                              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                               <horstretch>0</horstretch>
                               <verstretch>0</verstretch>
                              </sizepolicy>
                             </property>
                             <property name="minimumSize">
                              <size>
                               <width>0</width>
                               <height>0</height>
                              </size>
                             </property>
                             <property name="font">
                              <font>
                               <family>PingFang</family>
                               <weight>75</weight>
                               <bold>true</bold>
                              </font>
                             </property>
                             <property name="toolTip">
                              <string>发送所设置的CE大小</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                             <property name="suffix">
                              <string/>
                             </property>
                             <property name="minimum">
                              <number>-128</number>
                             </property>
                             <property name="maximum">
                              <number>128</number>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QPushButton" name="send_ce_bt">
                             <property name="text">
                              <string>发送</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <widget class="Line" name="line_2">
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_23">
                           <item>
                            <widget class="QLabel" name="send_ept_lb">
                             <property name="minimumSize">
                              <size>
                               <width>0</width>
                               <height>25</height>
                              </size>
                             </property>
                             <property name="font">
                              <font>
                               <family>PingFang</family>
                               <weight>75</weight>
                               <bold>true</bold>
                              </font>
                             </property>
                             <property name="text">
                              <string>EPT:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QComboBox" name="send_ept_cb">
                             <item>
                              <property name="text">
                               <string>充满 / 01</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>故障 / 02</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>过温 / 03</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>过压 / 04</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>过流 / 05</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>电池故障 / 06</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>工作点异常 / 08</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>协商异常 / 0A</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>重启 / 0B</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>Reping / 0C</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>NFC / 0D</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>NA / 0E</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>NA / 0F</string>
                              </property>
                             </item>
                            </widget>
                           </item>
                           <item>
                            <widget class="QPushButton" name="send_ept_bt">
                             <property name="text">
                              <string>发送</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <widget class="Line" name="line_14">
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QLabel" name="label_65">
                           <property name="font">
                            <font>
                             <pointsize>8</pointsize>
                            </font>
                           </property>
                           <property name="text">
                            <string>指定数据包：（16进制，空格分隔）</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_20">
                           <item>
                            <widget class="QLineEdit" name="protocol_data_ld">
                             <property name="inputMask">
                              <string/>
                             </property>
                             <property name="text">
                              <string>18 01 19</string>
                             </property>
                             <property name="maxLength">
                              <number>32767</number>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QPushButton" name="protocol_send_data_bt">
                             <property name="text">
                              <string>发送</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="start_auto_test_bt">
                        <property name="text">
                         <string>启动自动化协议检测</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <spacer name="verticalSpacer">
                        <property name="orientation">
                         <enum>Qt::Vertical</enum>
                        </property>
                        <property name="sizeHint" stdset="0">
                         <size>
                          <width>20</width>
                          <height>40</height>
                         </size>
                        </property>
                       </spacer>
                      </item>
                     </layout>
                    </widget>
                    <widget class="QWidget" name="tabWaveGen">
                     <attribute name="title">
                      <string>协议</string>
                     </attribute>
                     <attribute name="toolTip">
                      <string>协议信息查看</string>
                     </attribute>
                     <layout class="QVBoxLayout" name="verticalLayout_5">
                      <item>
                       <widget class="QGroupBox" name="groupBox_2">
                        <property name="font">
                         <font>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="title">
                         <string>EPP 数据</string>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_17">
                         <item>
                          <layout class="QGridLayout" name="gridLayout_data_2">
                           <property name="leftMargin">
                            <number>9</number>
                           </property>
                           <property name="topMargin">
                            <number>9</number>
                           </property>
                           <property name="rightMargin">
                            <number>9</number>
                           </property>
                           <property name="bottomMargin">
                            <number>9</number>
                           </property>
                           <item row="3" column="1">
                            <widget class="QLabel" name="support_dup_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="4" column="1">
                            <widget class="QLabel" name="support_ob_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="1" column="1">
                            <widget class="QLabel" name="manufacturer_id_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="1" column="0">
                            <widget class="QLabel" name="label_22">
                             <property name="text">
                              <string>制造商 ID:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="3" column="0">
                            <widget class="QLabel" name="Coil_temp_bt_2">
                             <property name="text">
                              <string>双工通信:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="6" column="0">
                            <widget class="QLabel" name="label_27">
                             <property name="text">
                              <string>NRS 比例:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="5" column="1">
                            <widget class="QLabel" name="wpid_value_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="2" column="1">
                            <widget class="QLabel" name="support_ar_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="4" column="0">
                            <widget class="QLabel" name="label_34">
                             <property name="text">
                              <string>带外通信:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="5" column="0">
                            <widget class="QLabel" name="label_25">
                             <property name="text">
                              <string>WPID:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="2" column="0">
                            <widget class="QLabel" name="label_28">
                             <property name="text">
                              <string>支持鉴权:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="6" column="1">
                            <widget class="QLabel" name="nrs_value_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="0" column="0">
                            <widget class="QLabel" name="label_20">
                             <property name="text">
                              <string>TX Qi版本:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="0" column="1">
                            <widget class="QLabel" name="qi_version_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QGroupBox" name="groupBox_3">
                        <property name="font">
                         <font>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="title">
                         <string>MPP 数据</string>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_20">
                         <item>
                          <layout class="QGridLayout" name="gridLayout_data_3">
                           <property name="leftMargin">
                            <number>9</number>
                           </property>
                           <property name="topMargin">
                            <number>9</number>
                           </property>
                           <property name="rightMargin">
                            <number>9</number>
                           </property>
                           <property name="bottomMargin">
                            <number>9</number>
                           </property>
                           <item row="6" column="1">
                            <widget class="QLabel" name="app_value_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="2" column="1">
                            <widget class="QLabel" name="support_calibration_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="3" column="1">
                            <widget class="QLabel" name="buffer_size_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="0" column="0">
                            <widget class="QLabel" name="label_51">
                             <property name="minimumSize">
                              <size>
                               <width>0</width>
                               <height>0</height>
                              </size>
                             </property>
                             <property name="font">
                              <font>
                               <family>PingFang</family>
                               <pointsize>10</pointsize>
                              </font>
                             </property>
                             <property name="focusPolicy">
                              <enum>Qt::ClickFocus</enum>
                             </property>
                             <property name="text">
                              <string>设备ID:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                             <property name="margin">
                              <number>0</number>
                             </property>
                            </widget>
                           </item>
                           <item row="7" column="1">
                            <widget class="QLabel" name="uid_value_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="4" column="0">
                            <widget class="QLabel" name="label_49">
                             <property name="text">
                              <string>并发数据流:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="1" column="1">
                            <widget class="QLabel" name="power_limit_reason_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="0" column="1">
                            <widget class="QLabel" name="device_id_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="6" column="0">
                            <widget class="QLabel" name="Coil_temp_bt_3">
                             <property name="text">
                              <string>APP:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="4" column="1">
                            <widget class="QLabel" name="concurrent_streams_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="5" column="1">
                            <widget class="QLabel" name="g_coil_rx_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                            </widget>
                           </item>
                           <item row="2" column="0">
                            <widget class="QLabel" name="label_46">
                             <property name="sizePolicy">
                              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                               <horstretch>0</horstretch>
                               <verstretch>0</verstretch>
                              </sizepolicy>
                             </property>
                             <property name="minimumSize">
                              <size>
                               <width>0</width>
                               <height>0</height>
                              </size>
                             </property>
                             <property name="font">
                              <font>
                               <family>PingFang</family>
                               <pointsize>9</pointsize>
                              </font>
                             </property>
                             <property name="focusPolicy">
                              <enum>Qt::ClickFocus</enum>
                             </property>
                             <property name="acceptDrops">
                              <bool>false</bool>
                             </property>
                             <property name="layoutDirection">
                              <enum>Qt::RightToLeft</enum>
                             </property>
                             <property name="text">
                              <string>支持校准:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                             <property name="margin">
                              <number>0</number>
                             </property>
                            </widget>
                           </item>
                           <item row="1" column="0">
                            <widget class="QLabel" name="label_54">
                             <property name="text">
                              <string>功率限制原因:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="5" column="0">
                            <widget class="QLabel" name="label_55">
                             <property name="text">
                              <string>g_goil_rx:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="7" column="0">
                            <widget class="QLabel" name="label_56">
                             <property name="text">
                              <string>UID:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item row="3" column="0">
                            <widget class="QLabel" name="label_47">
                             <property name="text">
                              <string>缓存大小:</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <spacer name="verticalSpacer_2">
                        <property name="orientation">
                         <enum>Qt::Vertical</enum>
                        </property>
                        <property name="sizeHint" stdset="0">
                         <size>
                          <width>20</width>
                          <height>40</height>
                         </size>
                        </property>
                       </spacer>
                      </item>
                     </layout>
                    </widget>
                    <widget class="QWidget" name="tabBat">
                     <attribute name="title">
                      <string>充电模拟</string>
                     </attribute>
                     <attribute name="toolTip">
                      <string>电池充电模拟测试</string>
                     </attribute>
                     <layout class="QVBoxLayout" name="verticalLayout_26">
                      <property name="spacing">
                       <number>4</number>
                      </property>
                      <property name="leftMargin">
                       <number>5</number>
                      </property>
                      <property name="topMargin">
                       <number>5</number>
                      </property>
                      <property name="rightMargin">
                       <number>5</number>
                      </property>
                      <property name="bottomMargin">
                       <number>5</number>
                      </property>
                      <item>
                       <widget class="QPushButton" name="btnBatSim">
                        <property name="text">
                         <string>点击开启功能</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <layout class="QHBoxLayout" name="horizontalLayout_47" stretch="2">
                        <item>
                         <widget class="QLabel" name="labelBatSimTime">
                          <property name="minimumSize">
                           <size>
                            <width>0</width>
                            <height>25</height>
                           </size>
                          </property>
                          <property name="font">
                           <font>
                            <family>PingFang</family>
                            <weight>75</weight>
                            <bold>true</bold>
                           </font>
                          </property>
                          <property name="text">
                           <string>Charge Time: 00:00:00</string>
                          </property>
                          <property name="alignment">
                           <set>Qt::AlignCenter</set>
                          </property>
                          <property name="margin">
                           <number>6</number>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item>
                       <widget class="QScrollArea" name="scrollAreaBatSim">
                        <property name="frameShape">
                         <enum>QFrame::NoFrame</enum>
                        </property>
                        <property name="widgetResizable">
                         <bool>true</bool>
                        </property>
                        <widget class="QWidget" name="scrollAreaWidgetContents_5">
                         <property name="geometry">
                          <rect>
                           <x>0</x>
                           <y>0</y>
                           <width>358</width>
                           <height>448</height>
                          </rect>
                         </property>
                         <layout class="QVBoxLayout" name="verticalLayout_25">
                          <property name="spacing">
                           <number>8</number>
                          </property>
                          <item>
                           <widget class="QGroupBox" name="groupBoxChargeConfig">
                            <property name="font">
                             <font>
                              <family>PingFang</family>
                              <weight>75</weight>
                              <bold>true</bold>
                             </font>
                            </property>
                            <property name="title">
                             <string>🔋 充电配置</string>
                            </property>
                            <layout class="QVBoxLayout" name="verticalLayoutChargeConfig">
                             <property name="spacing">
                              <number>6</number>
                             </property>
                             <item>
                              <layout class="QHBoxLayout" name="horizontalLayout_48" stretch="1,1">
                               <item>
                                <widget class="QPushButton" name="btnBatSimLoad">
                                 <property name="font">
                                  <font>
                                   <family>PingFang</family>
                                   <weight>75</weight>
                                   <bold>true</bold>
                                  </font>
                                 </property>
                                 <property name="toolTip">
                                  <string>从文件加载充电曲线数据</string>
                                 </property>
                                 <property name="text">
                                  <string>加载曲线</string>
                                 </property>
                                </widget>
                               </item>
                               <item>
                                <widget class="QPushButton" name="btnBatSimPreview">
                                 <property name="font">
                                  <font>
                                   <family>PingFang</family>
                                   <weight>75</weight>
                                   <bold>true</bold>
                                  </font>
                                 </property>
                                 <property name="toolTip">
                                  <string>预览当前充电曲线</string>
                                 </property>
                                 <property name="text">
                                  <string>查看曲线</string>
                                 </property>
                                </widget>
                               </item>
                              </layout>
                             </item>
                             <item>
                              <layout class="QHBoxLayout" name="horizontalLayout_42" stretch="2,3">
                               <item>
                                <widget class="QLabel" name="label_32">
                                 <property name="minimumSize">
                                  <size>
                                   <width>0</width>
                                   <height>20</height>
                                  </size>
                                 </property>
                                 <property name="font">
                                  <font>
                                   <family>PingFang</family>
                                   <weight>75</weight>
                                   <bold>true</bold>
                                  </font>
                                 </property>
                                 <property name="text">
                                  <string>充电曲线</string>
                                 </property>
                                 <property name="alignment">
                                  <set>Qt::AlignCenter</set>
                                 </property>
                                </widget>
                               </item>
                               <item>
                                <widget class="QComboBox" name="comboBatSimCurve">
                                 <property name="font">
                                  <font>
                                   <family>PingFang</family>
                                   <weight>75</weight>
                                   <bold>true</bold>
                                  </font>
                                 </property>
                                 <property name="toolTip">
                                  <string>选择要使用的充电曲线类型</string>
                                 </property>
                                </widget>
                               </item>
                              </layout>
                             </item>
                            </layout>
                           </widget>
                          </item>
                          <item>
                           <widget class="QGroupBox" name="groupBoxChargeControl">
                            <property name="font">
                             <font>
                              <family>PingFang</family>
                              <weight>75</weight>
                              <bold>true</bold>
                             </font>
                            </property>
                            <property name="title">
                             <string>⚡ 充电控制</string>
                            </property>
                            <layout class="QGridLayout" name="gridLayoutChargeControl">
                             <property name="spacing">
                              <number>8</number>
                             </property>
                             <item row="1" column="0">
                              <widget class="QLabel" name="label_37">
                               <property name="minimumSize">
                                <size>
                                 <width>0</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="text">
                                <string>目标电量</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                              </widget>
                             </item>
                             <item row="0" column="0">
                              <widget class="QLabel" name="label_35">
                               <property name="minimumSize">
                                <size>
                                 <width>0</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="text">
                                <string>当前电量</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                              </widget>
                             </item>
                             <item row="0" column="1">
                              <widget class="QDoubleSpinBox" name="spinBoxBatSimCurrent">
                               <property name="minimumSize">
                                <size>
                                 <width>100</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="toolTip">
                                <string>设置电池当前电量百分比</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                               <property name="suffix">
                                <string>%</string>
                               </property>
                               <property name="decimals">
                                <number>1</number>
                               </property>
                               <property name="minimum">
                                <double>0.000000000000000</double>
                               </property>
                               <property name="maximum">
                                <double>100.000000000000000</double>
                               </property>
                               <property name="singleStep">
                                <double>1.000000000000000</double>
                               </property>
                               <property name="value">
                                <double>0.000000000000000</double>
                               </property>
                              </widget>
                             </item>
                             <item row="1" column="1">
                              <widget class="QDoubleSpinBox" name="spinBoxBatSimStop">
                               <property name="minimumSize">
                                <size>
                                 <width>100</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="toolTip">
                                <string>设置充电目标电量百分比</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                               <property name="suffix">
                                <string>%</string>
                               </property>
                               <property name="decimals">
                                <number>1</number>
                               </property>
                               <property name="minimum">
                                <double>0.000000000000000</double>
                               </property>
                               <property name="maximum">
                                <double>100.000000000000000</double>
                               </property>
                               <property name="singleStep">
                                <double>1.000000000000000</double>
                               </property>
                               <property name="value">
                                <double>100.000000000000000</double>
                               </property>
                              </widget>
                             </item>
                             <item row="2" column="0">
                              <widget class="QLabel" name="label_38">
                               <property name="minimumSize">
                                <size>
                                 <width>0</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="text">
                                <string>执行频率</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                              </widget>
                             </item>
                             <item row="2" column="1">
                              <widget class="QDoubleSpinBox" name="spinBoxBatSimLoopFreq">
                               <property name="minimumSize">
                                <size>
                                 <width>100</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="toolTip">
                                <string>设置充电模拟计算频率</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                               <property name="suffix">
                                <string>Hz</string>
                               </property>
                               <property name="decimals">
                                <number>1</number>
                               </property>
                               <property name="minimum">
                                <double>1.000000000000000</double>
                               </property>
                               <property name="maximum">
                                <double>100.000000000000000</double>
                               </property>
                               <property name="singleStep">
                                <double>1.000000000000000</double>
                               </property>
                               <property name="value">
                                <double>30.000000000000000</double>
                               </property>
                              </widget>
                             </item>
                            </layout>
                           </widget>
                          </item>
                          <item>
                           <widget class="QGroupBox" name="groupBoxBatteryParams">
                            <property name="font">
                             <font>
                              <family>PingFang</family>
                              <weight>75</weight>
                              <bold>true</bold>
                             </font>
                            </property>
                            <property name="title">
                             <string>🔧 电池参数</string>
                            </property>
                            <layout class="QGridLayout" name="gridLayoutBatteryParams">
                             <property name="spacing">
                              <number>8</number>
                             </property>
                             <item row="0" column="0">
                              <widget class="QLabel" name="label_33">
                               <property name="minimumSize">
                                <size>
                                 <width>0</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="text">
                                <string>单节容量</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                              </widget>
                             </item>
                             <item row="0" column="1">
                              <widget class="QDoubleSpinBox" name="spinBoxBatSimCap">
                               <property name="minimumSize">
                                <size>
                                 <width>100</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="toolTip">
                                <string>设置单节电池的能量容量</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                               <property name="suffix">
                                <string>Wh</string>
                               </property>
                               <property name="decimals">
                                <number>1</number>
                               </property>
                               <property name="minimum">
                                <double>0.100000000000000</double>
                               </property>
                               <property name="maximum">
                                <double>999.000000000000000</double>
                               </property>
                               <property name="singleStep">
                                <double>0.500000000000000</double>
                               </property>
                               <property name="value">
                                <double>10.000000000000000</double>
                               </property>
                              </widget>
                             </item>
                             <item row="1" column="0">
                              <widget class="QLabel" name="label_40">
                               <property name="minimumSize">
                                <size>
                                 <width>0</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="text">
                                <string>单节内阻</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                              </widget>
                             </item>
                             <item row="1" column="1">
                              <widget class="QDoubleSpinBox" name="spinBoxBatSimRes">
                               <property name="minimumSize">
                                <size>
                                 <width>100</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="toolTip">
                                <string>设置单节电池的内阻值</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                               <property name="suffix">
                                <string>mΩ</string>
                               </property>
                               <property name="decimals">
                                <number>1</number>
                               </property>
                               <property name="minimum">
                                <double>0.000000000000000</double>
                               </property>
                               <property name="maximum">
                                <double>1000.000000000000000</double>
                               </property>
                               <property name="singleStep">
                                <double>1.000000000000000</double>
                               </property>
                               <property name="value">
                                <double>50.000000000000000</double>
                               </property>
                              </widget>
                             </item>
                             <item row="2" column="0">
                              <widget class="QLabel" name="label_41">
                               <property name="minimumSize">
                                <size>
                                 <width>0</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="text">
                                <string>串联节数</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                              </widget>
                             </item>
                             <item row="2" column="1">
                              <widget class="QSpinBox" name="spinBoxBatSimCells">
                               <property name="minimumSize">
                                <size>
                                 <width>100</width>
                                 <height>25</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>PingFang</family>
                                 <weight>75</weight>
                                 <bold>true</bold>
                                </font>
                               </property>
                               <property name="toolTip">
                                <string>设置电池串联的节数</string>
                               </property>
                               <property name="alignment">
                                <set>Qt::AlignCenter</set>
                               </property>
                               <property name="suffix">
                                <string>S</string>
                               </property>
                               <property name="minimum">
                                <number>1</number>
                               </property>
                               <property name="maximum">
                                <number>6</number>
                               </property>
                               <property name="value">
                                <number>1</number>
                               </property>
                              </widget>
                             </item>
                            </layout>
                           </widget>
                          </item>
                          <item>
                           <spacer name="verticalSpacer_6">
                            <property name="orientation">
                             <enum>Qt::Vertical</enum>
                            </property>
                            <property name="sizeHint" stdset="0">
                             <size>
                              <width>20</width>
                              <height>40</height>
                             </size>
                            </property>
                           </spacer>
                          </item>
                         </layout>
                        </widget>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                    <widget class="QWidget" name="tab">
                     <attribute name="title">
                      <string>设置</string>
                     </attribute>
                     <layout class="QVBoxLayout" name="verticalLayout_15">
                      <item>
                       <widget class="QGroupBox" name="groupBoxChargeConfig_5">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="font">
                         <font>
                          <family>PingFang</family>
                          <weight>75</weight>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="title">
                         <string>负载配置</string>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayoutChargeConfig_5">
                         <property name="spacing">
                          <number>6</number>
                         </property>
                         <item>
                          <layout class="QGridLayout" name="gridLayout">
                           <item row="0" column="1">
                            <widget class="QRadioButton" name="ext_eload_rb">
                             <property name="text">
                              <string>外部负载</string>
                             </property>
                            </widget>
                           </item>
                           <item row="0" column="0">
                            <widget class="QRadioButton" name="inter_eload_rb">
                             <property name="text">
                              <string>内部负载</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QGroupBox" name="groupBox">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="title">
                         <string>设备信息</string>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_4">
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_15">
                           <item>
                            <widget class="QLabel" name="label_21">
                             <property name="text">
                              <string>固件版本：</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLabel" name="fw_ver_lb">
                             <property name="text">
                              <string>0.0.0</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_14">
                           <item>
                            <widget class="QLabel" name="label_15">
                             <property name="text">
                              <string>内核版本：</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLabel" name="bl_ver">
                             <property name="text">
                              <string>0.0.0</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_18">
                           <item>
                            <widget class="QLabel" name="label_9">
                             <property name="text">
                              <string>硬件版本：</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLabel" name="hw_lb">
                             <property name="text">
                              <string>0.0.0</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <widget class="QPushButton" name="check_ver_bt">
                           <property name="text">
                            <string>查询版本</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_9">
                           <item>
                            <widget class="QLineEdit" name="SN_let"/>
                           </item>
                           <item>
                            <widget class="QPushButton" name="check_sn_bt">
                             <property name="text">
                              <string>查询 SN</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QGroupBox" name="groupBoxChargeConfig_3">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="font">
                         <font>
                          <family>PingFang</family>
                          <weight>75</weight>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="title">
                         <string>系统设置</string>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_21">
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_10">
                           <item>
                            <widget class="QLabel" name="label_36">
                             <property name="text">
                              <string>电压补偿：</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QDoubleSpinBox" name="vol_comp_sb"/>
                           </item>
                           <item>
                            <widget class="QPushButton" name="vol_comp_bt">
                             <property name="text">
                              <string>设置</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_8">
                           <item>
                            <widget class="QLabel" name="label_45">
                             <property name="text">
                              <string>电流补偿：</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QDoubleSpinBox" name="curr_comp_sb">
                             <property name="minimum">
                              <double>-110.000000000000000</double>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QPushButton" name="curr_comp_bt">
                             <property name="text">
                              <string>设置</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_11">
                           <item>
                            <widget class="QLabel" name="label_52">
                             <property name="text">
                              <string>频率补偿：</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QDoubleSpinBox" name="freq_comp_sb"/>
                           </item>
                           <item>
                            <widget class="QPushButton" name="freq_comp_bt">
                             <property name="text">
                              <string>设置</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <widget class="Line" name="line_12">
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QPushButton" name="factor_reset_bt">
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>30</height>
                            </size>
                           </property>
                           <property name="text">
                            <string>恢复出厂设置</string>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <spacer name="verticalSpacer_3">
                        <property name="orientation">
                         <enum>Qt::Vertical</enum>
                        </property>
                        <property name="sizeHint" stdset="0">
                         <size>
                          <width>20</width>
                          <height>40</height>
                         </size>
                        </property>
                       </spacer>
                      </item>
                     </layout>
                    </widget>
                    <widget class="QWidget" name="tab_2">
                     <attribute name="title">
                      <string>图表</string>
                     </attribute>
                     <layout class="QVBoxLayout" name="verticalLayout_29">
                      <item>
                       <widget class="QGroupBox" name="groupBox_4">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="title">
                         <string>在线数据/条件</string>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_9">
                         <item>
                          <widget class="QSpinBox" name="spinBox">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QSpinBox" name="spinBox_2">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QRadioButton" name="radioButton">
                           <property name="autoFillBackground">
                            <bool>false</bool>
                           </property>
                           <property name="text">
                            <string>满足条件自动停止</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QRadioButton" name="radioButton_2">
                           <property name="text">
                            <string>使用系统时间开始记录</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QRadioButton" name="radioButton_3">
                           <property name="text">
                            <string>边采边保存</string>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QGroupBox" name="groupBox_6">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="title">
                         <string>存储深度</string>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_28">
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_29">
                           <item>
                            <widget class="QLabel" name="label_44">
                             <property name="text">
                              <string>采样率：</string>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QComboBox" name="comboSampleRate">
                             <property name="sizePolicy">
                              <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                               <horstretch>0</horstretch>
                               <verstretch>0</verstretch>
                              </sizepolicy>
                             </property>
                             <property name="minimumSize">
                              <size>
                               <width>80</width>
                               <height>0</height>
                              </size>
                             </property>
                             <property name="maximumSize">
                              <size>
                               <width>120</width>
                               <height>16777215</height>
                              </size>
                             </property>
                             <property name="font">
                              <font>
                               <family>PingFang</family>
                              </font>
                             </property>
                             <item>
                              <property name="text">
                               <string>1SPS</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>10SPS</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>50SPS</string>
                              </property>
                             </item>
                             <item>
                              <property name="text">
                               <string>1KSPS</string>
                              </property>
                             </item>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_25">
                           <item>
                            <widget class="QSpinBox" name="storage_depth_sp">
                             <property name="sizePolicy">
                              <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                               <horstretch>0</horstretch>
                               <verstretch>0</verstretch>
                              </sizepolicy>
                             </property>
                             <property name="maximum">
                              <number>2000</number>
                             </property>
                             <property name="singleStep">
                              <number>10</number>
                             </property>
                             <property name="value">
                              <number>20</number>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLabel" name="label_18">
                             <property name="sizePolicy">
                              <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
                               <horstretch>0</horstretch>
                               <verstretch>0</verstretch>
                              </sizepolicy>
                             </property>
                             <property name="text">
                              <string>MB</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_26">
                           <item>
                            <widget class="QLabel" name="label_23">
                             <property name="text">
                              <string>缓存数据：</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLabel" name="record_duration_lb">
                             <property name="text">
                              <string>xxx</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                         <item>
                          <layout class="QHBoxLayout" name="horizontalLayout_28">
                           <item>
                            <widget class="QLabel" name="label_42">
                             <property name="text">
                              <string>当前记录数据：</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLabel" name="num_samples_lb">
                             <property name="text">
                              <string>0</string>
                             </property>
                             <property name="alignment">
                              <set>Qt::AlignCenter</set>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLabel" name="label_14">
                             <property name="text">
                              <string>/</string>
                             </property>
                            </widget>
                           </item>
                           <item>
                            <widget class="QLabel" name="max_samples_lb">
                             <property name="text">
                              <string>100000</string>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <spacer name="verticalSpacer_5">
                        <property name="orientation">
                         <enum>Qt::Vertical</enum>
                        </property>
                        <property name="sizeHint" stdset="0">
                         <size>
                          <width>20</width>
                          <height>40</height>
                         </size>
                        </property>
                       </spacer>
                      </item>
                     </layout>
                    </widget>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QFrame" name="frameSystemSetting">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>PingFang</family>
                </font>
               </property>
               <property name="frameShape">
                <enum>QFrame::StyledPanel</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_13" stretch="0,0">
                <property name="leftMargin">
                 <number>3</number>
                </property>
                <property name="topMargin">
                 <number>3</number>
                </property>
                <property name="rightMargin">
                 <number>3</number>
                </property>
                <property name="bottomMargin">
                 <number>3</number>
                </property>
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout_54"/>
                </item>
                <item>
                 <layout class="QVBoxLayout" name="verticalLayout_14">
                  <property name="spacing">
                   <number>3</number>
                  </property>
                  <item>
                   <widget class="QPushButton" name="ProtocolButton">
                    <property name="text">
                     <string>Qi协议分析仪</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnUpdatings">
                    <property name="minimumSize">
                     <size>
                      <width>30</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>999</width>
                      <height>16777215</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>软件/固件更新</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_21">
                    <property name="topMargin">
                     <number>0</number>
                    </property>
                    <item>
                     <widget class="QPushButton" name="btnGraphics">
                      <property name="minimumSize">
                       <size>
                        <width>0</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>999</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>PingFang</family>
                       </font>
                      </property>
                      <property name="text">
                       <string>图形设置</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="btnSettings">
                      <property name="minimumSize">
                       <size>
                        <width>0</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>999</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>PingFang</family>
                       </font>
                      </property>
                      <property name="text">
                       <string>⚙系统设置</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_12" stretch="1,1">
                    <item>
                     <widget class="QLabel" name="labelConnectState">
                      <property name="font">
                       <font>
                        <family>PingFang</family>
                        <weight>50</weight>
                        <italic>false</italic>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>未连接</string>
                      </property>
                      <property name="textFormat">
                       <enum>Qt::AutoText</enum>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignCenter</set>
                      </property>
                      <property name="margin">
                       <number>3</number>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="btnConnect">
                      <property name="minimumSize">
                       <size>
                        <width>0</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>PingFang</family>
                       </font>
                      </property>
                      <property name="text">
                       <string>连接/断开</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PlotWidget</class>
   <extends>QWidget</extends>
   <header>pyqtgraph</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>AppleStyleDisplay</class>
   <extends>QWidget</extends>
   <header>mdp_gui_template.applestyledisplay</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
